'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Pen, Trash } from 'lucide-react';
import { UseGetTrustedPartners } from '../queries/use-get-trusted-partners';
import { UseDeleteTrustedPartner } from '../mutations/delete-trusted-partner';
import Image from 'next/image';

const TrustedPartnerListPage: React.FC = () => {

    const { data, isLoading, isError } = UseGetTrustedPartners();

    const trustedPartner = data?.data || null;

    const { mutate: deleteSupport } = UseDeleteTrustedPartner();

    const handleDelete = () => {
        if (window.confirm('Are you sure you want to delete this support item?')) {
            deleteSupport(undefined, {
                onSuccess: () => alert('Support item deleted successfully'),
                onError: () => alert('Failed to delete support item'),
            });
        }
    };
    if (isLoading) return <div>Loading trusted partner section…</div>;
    if (isError) return <div>Error loading trusted partner section.</div>;

    if (!trustedPartner) {
        return (
            <div className="p-6">
                <p className="mb-6">No trusted partner section found.</p>
                <Link href="/home/<USER>/create">
                    <button className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition">
                        Create Trusted Partner Section
                    </button>
                </Link>
            </div>
        );
    }

    return (
        <section className="p-6">
            <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-3xl font-bold">Trusted Partners</h1>
                    <Link href="/home/<USER>/edit">
                        <Button>Edit</Button>
                    </Link>
                </div>

                <div className="overflow-x-auto bg-white rounded shadow">
                    <table className="min-w-full table-auto text-left">
                        <thead className="bg-gray-100">
                            <tr>
                                <th className="border px-4 py-2">S.N.</th>
                                <th className="border px-4 py-2">Description</th>
                                <th className="border px-4 py-2 w-48">Images</th>
                                <th className="border px-4 py-2 w-40">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr className="bg-white">
                                <td className="border px-4 py-2 align-top">1</td>

                                <td className="border px-4 py-2 align-top max-w-xs truncate">
                                    {trustedPartner.description}
                                </td>

                                <td className="border px-4 py-2 align-top">
                                    <div className="flex gap-2 flex-wrap max-w-xs">
                                        {trustedPartner.images.map((image) => (
                                            <Image
                                                src={image}
                                                alt={trustedPartner.description}
                                                width={80}
                                                height={60}
                                                className="rounded object-cover inline-block"
                                            />
                                        ))}
                                    </div>
                                </td>

                                <td className="border px-4 py-2 align-top">
                                    <div className="flex gap-2">
                                        <Link href={`/home/<USER>/edit`}>
                                            <button className="px-3 py-1 border border-green-600 text-green-600 rounded hover:bg-green-50 transition-colors">
                                                <Pen className="h-4 w-4" />
                                            </button>
                                        </Link>
                                        <button
                                            onClick={() => handleDelete(id)}
                                            className="px-3 py-1 border border-red-600 text-red-600 rounded hover:bg-red-50 transition-colors"
                                            type="button"
                                        >
                                            <Trash className="h-4 w-4" />
                                        </button>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    );
};

export default TrustedPartnerListPage;
