'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import FileUpload from '@/components/image/image-upload';
import { UseGetTrustedPartners } from '../queries/use-get-trusted-partners';
import { useUpdateTrustedPartner } from '../mutations/update-trusted-partner';
import { Textarea } from '@/components/ui/textarea';

const TrustedPartnerEditPage: React.FC = () => {
  const router = useRouter();
  const { data, isLoading, isError } = UseGetTrustedPartners();


  const trustedPartner = data?.data ?? null;

  const [description, setDescription] = useState('');
  const [images, setImages] = useState<string[]>([]);

  useEffect(() => {
    if (trustedPartner) {
      setDescription(trustedPartner.description);
      setImages(trustedPartner.images);
    }
  }, [trustedPartner]);

  const { mutate: updateTrustedPartner } = useUpdateTrustedPartner();

  const handleUpdate = () => {
    if (!trustedPartner) {
      alert('No trusted partner to update');
      return;
    }
    if (!description.trim()) {
      alert('Description is required');
      return;
    }
    if (images.length === 0) {
      alert('Please upload at least one image');
      return;
    }

    updateTrustedPartner(
      {
        id: trustedPartner.id,
        description,
        images,
      },
      {
        onSuccess: () => {
          alert('Trusted Partner updated successfully');
          router.push('/home/<USER>');
        },
        onError: () => {
          alert('Failed to update trusted partner');
        },
      }
    );
  };

  if (isLoading) return <div>Loading...</div>;

  if (isError || !trustedPartner)
    return (
      <div className="p-6 max-w-md mx-auto">
        <p>Trusted Partner not found or failed to load.</p>
        <Button onClick={() => router.push('/home')}>Back to List</Button>
      </div>
    );

  return (
    <div className="min-h-screen bg-gray-50 p-6 container mx-auto">
      <h2 className="text-2xl font-semibold mb-6">Edit Trusted Partner</h2>

      <div className="mb-6">
        <label className="block font-medium mb-2">Description</label>
        <Textarea
          className="w-full border rounded p-3 min-h-[120px]"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter description"
        />
      </div>

      <div className="mb-6">
        <FileUpload
          value={images}
          onChange={(urls) => {
            if (!urls) setImages([]);
            else if (Array.isArray(urls)) setImages(urls);
            else setImages([urls]);
          }}
          multiple={true}
          accept="image/*"
          label="Upload Image(s)"
        />
      </div>

      <div className="flex gap-4">
        <Button onClick={handleUpdate} className="bg-green-600 hover:bg-green-700 text-white">
          Update
        </Button>
        <Button onClick={() => router.push('/home/<USER>')} variant="secondary">
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default TrustedPartnerEditPage;
